"""
Report Generator

Handles the core business logic for generating WhatsApp reports.
"""
import sys
import os
from datetime import datetime

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from translations import get_translation


def generate_whatsapp_performance_report(campaign_data, language="pt"):
    """
    Generate WhatsApp performance report for a campaign.

    Args:
        campaign_data: Campaign data dictionary
        language: Language code for translations

    Returns:
        Formatted WhatsApp report string
    """
    days_running = (datetime.fromisoformat(campaign_data['last_updated']) -
                    datetime.fromisoformat(campaign_data['date_created'])).days + 1
    average_daily_cost = campaign_data['metrics']['cost'] / days_running if days_running > 0 else 0


    def format_currency(value, locale="pt_BR"):
        # Get the currency symbol from the translation system
        currency_symbol = get_translation('currency_symbol', locale)
        return f"{currency_symbol} {value:,.2f}".replace(',', 'vtemp').replace('.', ',').replace('vtemp', '.')

    # Calculate values needed for translations
    ads_sales = campaign_data['metrics']['total_amount']
    organic_sales = campaign_data['metrics']['organic_units_amount']
    total_sales = ads_sales + organic_sales

    # Calculate ROAS
    roas = campaign_data['metrics']['total_amount'] / campaign_data['metrics']['cost'] if campaign_data['metrics']['cost'] > 0 else 0

    # Initialize variables that might be used in translations
    deviation = 0
    total = 0
    ad_sales_percentage = 0
    organic_sales_percentage = 0
    lost_rank_share = 0
    lost_budget_share = 0
    target_acos = campaign_data['acos_target']
    acos_benchmark = campaign_data['metrics'].get('acos_benchmark', 0)
    acos_difference = 0

    if campaign_data['metrics'].get('acos_benchmark') and campaign_data['acos_target']:
        acos_difference = abs(target_acos - acos_benchmark) / acos_benchmark * 100 if acos_benchmark else 0

    if campaign_data['metrics']['advertising_items_quantity'] > 0:
        total = campaign_data['metrics']['advertising_items_quantity'] + campaign_data['metrics']['organic_items_quantity']
        ad_sales_percentage = (campaign_data['metrics']['advertising_items_quantity'] / total) * 100 if total > 0 else 0
        organic_sales_percentage = (campaign_data['metrics']['organic_items_quantity'] / total) * 100 if total > 0 else 0

    if campaign_data['metrics'].get('lost_impression_share_by_ad_rank'):
        lost_rank_share = campaign_data['metrics']['lost_impression_share_by_ad_rank'] * 100

    if campaign_data['metrics'].get('lost_impression_share_by_budget'):
        lost_budget_share = campaign_data['metrics']['lost_impression_share_by_budget'] * 100

    if campaign_data['metrics']['acos'] > campaign_data['acos_target']:
        deviation = ((campaign_data['metrics']['acos'] / campaign_data['acos_target']) - 1) * 100

    report = []

    # Map language to locale
    locale = "pt_BR" if language == "pt" else "es_AR" if language == "es" else "pt_BR"

    # Format values for translation parameters
    formatted_cost = format_currency(campaign_data['metrics']['cost'], locale)
    formatted_avg_daily_cost = format_currency(average_daily_cost, locale)
    formatted_budget = format_currency(campaign_data['budget'], locale)
    formatted_total_sales = format_currency(total_sales, locale)
    formatted_ads_sales = format_currency(ads_sales, locale)
    formatted_organic_sales = format_currency(organic_sales, locale)

    # Add report sections using the centralized translation system
    report.append(f"*{get_translation('metrics_title', locale, campaign_name=campaign_data['name'])}*")
    report.append(f"{get_translation('acos_whatsapp', locale, acos_real=campaign_data['metrics']['acos'], acos_target=campaign_data['acos_target'])}")
    report.append(f"{get_translation('budget_spent', locale, cost=formatted_cost)}")
    report.append(f"{get_translation('daily_cost', locale, avg_daily_cost=formatted_avg_daily_cost, budget=formatted_budget)}")
    report.append(f"{get_translation('total_sales_whatsapp', locale, total_sales=formatted_total_sales, ads_sales=formatted_ads_sales, organic_sales=formatted_organic_sales)}")
    report.append(f"\n*{get_translation('main_indicators', locale)}*")

    # Add ACOS analysis
    _add_acos_analysis(report, campaign_data, locale, deviation, acos_difference, target_acos, acos_benchmark)

    # Add budget analysis
    _add_budget_analysis(report, average_daily_cost, campaign_data, locale)

    # Add ROAS analysis
    _add_roas_analysis(report, roas, locale)

    # Add ads performance analysis
    _add_ads_performance_analysis(report, campaign_data, locale, total, ad_sales_percentage, organic_sales_percentage)

    # Add suggestions
    _add_suggestions(report, campaign_data, locale, lost_rank_share, lost_budget_share, acos_difference, target_acos, acos_benchmark)

    return '\n'.join(report)


def _add_acos_analysis(report, campaign_data, locale, deviation, acos_difference, target_acos, acos_benchmark):
    """Add ACOS analysis to the report."""
    if campaign_data['metrics'].get('acos_benchmark'):
        # We already calculated deviation earlier, so we don't need to recalculate it here
        deviation = ((campaign_data['metrics']['acos'] / campaign_data['acos_target']) - 1) * 100 if campaign_data['metrics']['acos'] > campaign_data['acos_target'] else 0

        if campaign_data['metrics']['acos'] > campaign_data['acos_target']:
            if campaign_data['metrics']['acos'] < campaign_data['metrics']['acos_benchmark']:
                report.append(f"{get_translation('acos_higher_below', locale, deviation=deviation, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}")
            else:
                report.append(f"{get_translation('acos_higher_above', locale, deviation=deviation, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}")
        elif campaign_data['metrics']['acos'] < campaign_data['acos_target']:
            if campaign_data['metrics']['acos'] < campaign_data['metrics']['acos_benchmark']:
                report.append(f"{get_translation('acos_below_benchmark', locale, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}")
            else:
                report.append(f"{get_translation('acos_below_near', locale, acos_benchmark=campaign_data['metrics']['acos_benchmark'])}")
        else:
            report.append(f"{get_translation('acos_expected', locale)}")


def _add_budget_analysis(report, average_daily_cost, campaign_data, locale):
    """Add budget analysis to the report."""
    if round(average_daily_cost) > campaign_data['budget']:
        report.append(f"{get_translation('budget_above', locale)}")
    else:
        report.append(f"{get_translation('budget_expected', locale)}")


def _add_roas_analysis(report, roas, locale):
    """Add ROAS analysis to the report."""
    if roas > 5:
        report.append(f"{get_translation('roas_impressive', locale, roas=roas)}")
    elif 2 <= roas <= 5:
        report.append(f"{get_translation('roas_good', locale, roas=roas)}")
    else:
        report.append(f"{get_translation('roas_low', locale, roas=roas)}")


def _add_ads_performance_analysis(report, campaign_data, locale, total, ad_sales_percentage, organic_sales_percentage):
    """Add ads performance analysis to the report."""
    report.append(f"\n*{get_translation('ads_performance', locale)}*")
    if campaign_data['metrics']['advertising_items_quantity'] > 0:
        # Calculate these values for translation parameters
        total = campaign_data['metrics']['advertising_items_quantity'] + campaign_data['metrics']['organic_items_quantity']
        ad_sales_percentage = (campaign_data['metrics']['advertising_items_quantity'] / total) * 100 if total > 0 else 0
        organic_sales_percentage = (campaign_data['metrics']['organic_items_quantity'] / total) * 100 if total > 0 else 0

        report.append(f"*{get_translation('positive_effect', locale, total=total)}*")
        report.append(f"   - *{get_translation('via_ads', locale, ad_quantity=campaign_data['metrics']['advertising_items_quantity'], ad_percentage=ad_sales_percentage)}*")
        report.append(f"   - *{get_translation('organic', locale, organic_quantity=campaign_data['metrics']['organic_items_quantity'], organic_percentage=organic_sales_percentage)}*")
        report.append(f"- {get_translation('cvr', locale, cvr=campaign_data['metrics']['cvr'])}")
    else:
        report.append(f"*{get_translation('no_results', locale)}*")


def _add_suggestions(report, campaign_data, locale, lost_rank_share, lost_budget_share, acos_difference, target_acos, acos_benchmark):
    """Add suggestions to the report."""
    report.append(f"\n*{get_translation('suggestions', locale)}*")
    has_suggestions = False

    if campaign_data['metrics'].get('lost_impression_share_by_ad_rank', 0) > 0.3:
        has_suggestions = True
        # Calculate lost_rank_share for translation parameter
        lost_rank_share = campaign_data['metrics']['lost_impression_share_by_ad_rank'] * 100
        report.append(f"{get_translation('improve_rank', locale, lost_rank=lost_rank_share)}")

    if campaign_data['metrics'].get('lost_impression_share_by_budget', 0) > 0.2:
        has_suggestions = True
        # Calculate lost_budget_share for translation parameter
        lost_budget_share = campaign_data['metrics']['lost_impression_share_by_budget'] * 100
        report.append(f"{get_translation('increase_budget', locale, lost_budget=lost_budget_share)}")

    if campaign_data['metrics'].get('acos') and campaign_data['metrics'].get('acos_benchmark'):
        # We already calculated these values earlier
        if acos_difference > 15:
            has_suggestions = True
            if target_acos > acos_benchmark:
                report.append(f"{get_translation('adjust_acos_above', locale, target_acos=target_acos, acos_difference=acos_difference, acos_benchmark=acos_benchmark)}")
            else:
                report.append(f"{get_translation('adjust_acos_below', locale, target_acos=target_acos, acos_difference=acos_difference, acos_benchmark=acos_benchmark)}")

    if not has_suggestions:
        report.append(f"*{get_translation('no_suggestions', locale)}*")
