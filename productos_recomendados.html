<!DOCTYPE html>
<html lang="es-LA">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Productos Recomendados para Campañas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .header-logo {
            max-height: 80px;
            margin: 20px 0;
        }
        .product-image {
            max-width: 100px;
            max-height: 100px;
            object-fit: contain;
        }
        .recommendation-badge {
            background-color: #10B981;
            color: white;
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }
        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #4F46E5;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center">
            <img src="https://gobots.ai/wp-content/uploads/2022/02/logo-gobots-solucao-platinum-mercado-livre-inteligencia-artificial-cor.png" alt="GoBots Logo" class="header-logo img-fluid">
            <h1 class="mt-4 mb-4">Productos Recomendados para Campañas de Anuncios</h1>
        </div>

        <div class="info-box">
            <h5>Tienda: <a href="{{store_permalink}}" target="_blank">{{store_name}}</a></h5>
            <p>Los productos a continuación fueron identificados como buenos candidatos para campañas de Product Ads, pero aún no están siendo anunciados.</p>
            <p>Agregar estos productos a sus campañas puede aumentar sus ventas y mejorar el rendimiento general de su tienda.</p>
        </div>

        {% if df_unlisted.shape[0] == 1 and 'product_group' not in df_unlisted.columns %}
        <!-- Case 1: Empty DataFrame with just store info -->
        <div class="alert alert-info mt-5">
            <h4 class="alert-heading">¡No se encontraron productos recomendados!</h4>
            <p>No encontramos productos que aún no estén en campañas y que sean recomendados para anuncios.</p>
            <p>Esto puede significar que ya está anunciando todos los productos recomendados, ¡lo cual es una excelente estrategia!</p>
        </div>
        {% else %}
        <!-- Case 2: DataFrame with products -->
        {% set recommended_products = df_unlisted %}

        {% if recommended_products.empty %}
        <!-- Case 2.1: DataFrame exists but is empty -->
        <div class="alert alert-info mt-5">
            <h4 class="alert-heading">¡No se encontraron productos recomendados!</h4>
            <p>No encontramos productos que aún no estén en campañas y que sean recomendados para anuncios.</p>
            <p>Esto puede significar que ya está anunciando todos los productos recomendados, ¡lo cual es una excelente estrategia!</p>
        </div>
        {% else %}
        <!-- Case 2.2: DataFrame has products -->
        <h2 class="mt-5">Productos Prioritarios para Anuncios</h2>
        <p>Estos productos tienen alto potencial de ventas y deben ser considerados prioritarios para sus campañas.</p>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th class="text-center">Producto</th>
                        <th class="text-center">Título</th>
                        <th class="text-center">Precio</th>
                        <th class="text-center">Visitas</th>
                        <th class="text-center">Ventas</th>
                        <th class="text-center">Conversión</th>
                        <th class="text-center">Potencial de Ventas</th>
                        <th class="text-center">Clase ABC</th>
                    </tr>
                </thead>
                <tbody>
                    {% for _, row in recommended_products.iterrows() %}
                    <tr>
                        <td class="text-center align-middle">
                            <span class="recommendation-badge">Recomendado</span><br>
                            <a href="{{ row['permalink'] }}" target="_blank">
                                <img src="{{ row['image_url'] }}" alt="Imagen del Producto" class="product-image">
                            </a>
                        </td>
                        <td class="align-middle">{{ row['title'] }}</td>
                        <td class="text-center align-middle">$ {{ "%.2f"|format(row['price']) }}</td>
                        <td class="text-center align-middle">{{ row['visits'] }}</td>
                        <td class="text-center align-middle">{{ row['sales'] }}</td>
                        <td class="text-center align-middle">{{ "%.1f"|format(row['conversion']*100) }}%</td>
                        <td class="text-center align-middle">$ {{ "%.2f"|format(row['sales_potential']) }}</td>
                        <td class="text-center align-middle">{{ row['abc_class'] }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        {% endif %}

        <div class="info-box mt-4 mb-5">
            <h5>Recomendaciones para Campañas de Product Ads</h5>
            {% set country_code = store_permalink.split('.')[0].split('//')[1].split('.')[1] %}
            {% set tutorial_url = {
                'com': 'https://vendedores.mercadolibre.com/nota/como-crear-campanas-publicitarias-en-product-ads',
                'com.ar': 'https://vendedores.mercadolibre.com.ar/nota/como-crear-campanas-publicitarias-en-product-ads',
                'com.mx': 'https://vendedores.mercadolibre.com.mx/nota/como-crear-campanas-publicitarias-en-product-ads',
                'com.br': 'https://vendedores.mercadolibre.com.br/nota/como-crear-campanas-publicitarias-en-product-ads',
                'cl': 'https://vendedores.mercadolibre.cl/nota/como-crear-campanas-publicitarias-en-product-ads',
                'com.co': 'https://vendedores.mercadolibre.com.co/nota/como-crear-campanas-publicitarias-en-product-ads'
            }.get(country_code, 'https://vendedores.mercadolibre.com/nota/como-crear-campanas-publicitarias-en-product-ads') %}
            <p>Mercado Libre tiene un <a href="{{ tutorial_url }}" target="_blank">tutorial explicativo</a> sobre cómo crear campañas de Product Ads.</p>
            <p>Nuestra sugerencia para el ACOS (Costo de Publicidad sobre Ventas) es entre 3% y 8%, dependiendo del margen del producto.</p>
            <p>Para mejores resultados, comience con productos que tienen alta tasa de conversión y buen potencial de ventas.</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

