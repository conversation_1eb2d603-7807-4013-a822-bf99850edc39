import os
from typing import Any, Dict, List, Optional, Union
import aiohttp

__access_token: Optional[str] = os.environ.get('GOBOTS_TOKEN')


def extract_user_id_from_token(access_token: str) -> str:
    return access_token.split("-")[-1]


def get_access_token_from_gobots_data(user_id: str, data: List[Dict[str, Any]]) -> Optional[str]:
    for item in data:
        if item.get('user_id') == user_id:
            return item.get('access_token')
    return None


async def get_api_response(session: aiohttp.ClientSession) -> Optional[Union[Dict[str, Any], List[Any]]]:
    url = 'https://askhere.gobots.com.br/ml/all'
    headers = {'Authorization': f'Bearer {__access_token}'}

    async with session.get(url, headers=headers) as response:
        if response.status == 200:
            return await response.json()
        return None


def filter_merchants_data(go_bots_data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Filter go_bots_data based on provided criteria.

    Args:
        go_bots_data (list): List of merchant data from GoBots API
        filters (dict): Dictionary containing filter criteria:
            - merchant_ids (list, optional): List of merchant IDs to filter by
            - seller_ids (list, optional): List of seller IDs to filter by
            - merchant_id (str, optional): Single merchant ID to filter by
            - seller_id (str, optional): Single seller ID to filter by

    Returns:
        list: List of matching merchant data with structure:
            [{"merchant": merchant_id, "access_token": token, "seller_id": seller_id}, ...]

    Raises:
        ValueError: If inputs are invalid
    """
    # Input validation
    if not isinstance(go_bots_data, list):
        raise ValueError("go_bots_data must be a list")

    if not isinstance(filters, dict):
        raise ValueError("filters must be a dictionary")

    if not go_bots_data:
        return []

    matches = []

    # Normalize inputs to lists with validation
    merchant_ids = filters.get('merchant_ids', [])
    if filters.get('merchant_id'):
        merchant_ids = [filters['merchant_id']]

    # Ensure merchant_ids is a list
    if not isinstance(merchant_ids, list):
        merchant_ids = [merchant_ids] if merchant_ids else []

    seller_ids = filters.get('seller_ids', [])
    if filters.get('seller_id'):
        seller_ids = [filters['seller_id']]

    # Ensure seller_ids is a list and convert to strings
    if not isinstance(seller_ids, list):
        seller_ids = [seller_ids] if seller_ids else []

    # Convert seller_ids to strings and filter out None/empty values
    seller_ids = [str(sid) for sid in seller_ids if sid is not None and str(sid).strip()]

    # If no merchant_ids provided, extract all unique merchant IDs
    if not merchant_ids:
        merchant_ids = list(set(
            item.get('merchant', {}).get('id')
            for item in go_bots_data
            if item.get('merchant') and item.get('merchant', {}).get('id')
        ))

    # Convert seller_ids to a set for O(1) lookup performance
    seller_ids_set = set(seller_ids) if seller_ids else None

    # Convert merchant_ids to a set for O(1) lookup performance
    merchant_ids_set = set(merchant_ids) if merchant_ids else None

    # Single pass through go_bots_data for better performance
    for item in go_bots_data:
        # Early validation - skip items that don't meet basic criteria
        if not (item.get('merchant') and
               item.get('merchant', {}).get('id') and
               item.get('access_token')):
            continue

        merchant_id = item['merchant']['id']

        # Skip if this merchant is not in our filter
        if merchant_ids_set and merchant_id not in merchant_ids_set:
            continue

        # Extract seller_id safely
        try:
            seller_id = item['access_token'].split("-")[-1]
        except (AttributeError, IndexError):
            continue  # Skip if access_token format is invalid

        # If seller_ids filter is provided, check if this seller matches
        if seller_ids_set and seller_id not in seller_ids_set:
            continue

        # Create and add the match
        merchant_match = {
            "merchant": merchant_id,
            "access_token": item['access_token'],
            "seller_id": seller_id
        }
        matches.append(merchant_match)

    return matches


def get_merchant_by_id(go_bots_data: List[Dict[str, Any]], merchant_id: str) -> Optional[Dict[str, str]]:
    """
    Get the first merchant match for a specific merchant ID.

    Args:
        go_bots_data (list): List of merchant data from GoBots API
        merchant_id (str): The merchant ID to find

    Returns:
        dict or None: First matching merchant data or None if not found
    """
    matches = filter_merchants_data(go_bots_data, {'merchant_id': merchant_id})
    return matches[0] if matches else None


def get_merchants_by_seller_ids(go_bots_data: List[Dict[str, Any]], seller_ids: List[str]) -> List[Dict[str, str]]:
    """
    Get all merchants matching the provided seller IDs.

    Args:
        go_bots_data (list): List of merchant data from GoBots API
        seller_ids (list): List of seller IDs to filter by

    Returns:
        list: List of matching merchant data
    """
    return filter_merchants_data(go_bots_data, {'seller_ids': seller_ids})
