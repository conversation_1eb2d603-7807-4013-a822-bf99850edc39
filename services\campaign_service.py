"""Service layer for campaign recommendations."""
from datetime import datetime
import pandas as pd
from utils.logger import get_logger
from input_data import calculate_metrics
from recommendation_report import filter_input
from repositories.campaign_repository import CampaignRepository

logger = get_logger(__name__)

class CampaignService:
    def __init__(self, repository: CampaignRepository):
        self.repository = repository

    async def process_recommendations(self, uid: str, users_data: list, date_from: str, date_to: str):
        logger.info(f"Processing user {uid}")

        # Get access token
        access_token = await self.repository.get_access_token(int(uid))
        if not access_token:
            logger.error(f"No access token for user {uid}")
            return None, None

        # Get advertiser ID
        logger.info(f"Fetching advertiser ID for user {uid}")
        advertiser_id = await self.repository.get_advertiser_id(access_token, uid)
        if not advertiser_id:
            logger.error(f"[ERRO] Não foi possível obter advertiser de PADS. Abortando {uid}.")
            return None, None

        # Get campaigns
        logger.info(f"Listing campaigns for user {uid} (advertiser {advertiser_id})")
        campaigns = await self.repository.get_campaigns(access_token, uid, advertiser_id, date_from, date_to)
        if not campaigns:
            logger.error(f"[ERRO] Nenhuma campanha encontrada {uid}.")
            return None, None

        # Get campaign products
        campaign_products = await self.repository.get_campaign_products(
            access_token, uid, advertiser_id, campaigns, date_from, date_to
        )

        # Get and process product data
        logger.info(f"Building output data for user {uid}")
        df = await self.repository.get_product_data(uid, access_token, 30)

        if df.shape[0] > 0:
            df = self._process_product_data(df)
            unlisted_items = self._find_unlisted_recommended_items(df, campaign_products)

            if unlisted_items:
                df_unlisted = df[df['item_id'].isin(unlisted_items)]
                return df_unlisted, None
            else:
                # Create empty DataFrame for "no products found" message
                empty_df = df.head(0)
                if not df.empty:
                    empty_df.loc[0, 'store_name'] = df['store_name'].iloc[0]
                    empty_df.loc[0, 'store_permalink'] = df['store_permalink'].iloc[0]
                return empty_df, "No unlisted recommended items found"

        return None, "No product data available"

    def _process_product_data(self, df):
        df = calculate_metrics(df)
        df['quality_score'] = df['quality_score'].astype('Int64')
        df['position'] = df['position'].astype('Int64')
        return filter_input(df)

    def _find_unlisted_recommended_items(self, df, campaign_products, min_items=3, max_items=8):
        df_rec = df[df['product_group'] == 2]
        df_rec = df_rec[~df_rec['sales_potential'].isna()]

        campaign_item_ids = [campaign['item_id'] for campaign in campaign_products]
        unlisted_recommended_items = []

        if len(df_rec['item_id'].tolist()) > min_items:
            unlisted_recommended_items = df_rec['item_id'].tolist()
        else:
            max_head = 3
            max_attempts = 5
            attempts = 0
            a_class_items_count = df[df['abc_class'] == "A"].shape[0]

            while (len(unlisted_recommended_items) < min_items and
                   attempts < max_attempts and
                   max_head <= a_class_items_count):
                logger.info(f"Trying with max_head = {max_head}")
                top_abc_items = df[df['abc_class'] == "A"].head(max_head)
                recommended_items = df_rec['item_id'].tolist() + top_abc_items['item_id'].tolist()
                unlisted_recommended_items = list(set(recommended_items) - set(campaign_item_ids))

                if len(unlisted_recommended_items) < min_items:
                    max_head *= 2
                    attempts += 1

        return unlisted_recommended_items[:max_items] if unlisted_recommended_items else []
